import { ajax } from "@/api/ajax";
import { formPromise } from "@/utils/fetch";

const prefix = "/api/v1"
export default {
  //标签配置相关API
  async getTagList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/tag/list`, {
      params
    }))
  },
  async addTag(data) {
    return await formPromise(ajax.post(`${prefix}/sda/tag/create`, data))
  },
  
  async updateTag(data) {
    return await formPromise(ajax.patch(`${prefix}/sda/tag/update`, data))
  },
  
  async deleteTag(id, params) {
    return await formPromise(ajax.delete(`${prefix}/sda/tag/delete/${id}`, { params }))
  },

  async getIpList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/host_tag/host/list`, {
      params
    }))
  },

  // 主机组规划相关API
  async getHostGroupPlanList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/host_group_plan/list`, {
      params
    }))
  },

  async addHostGroupPlan(data) {
    return await formPromise(ajax.post(`${prefix}/sda/host_group_plan/create`, data))
  },

  async updateHostGroupPlan(data) {
    return await formPromise(ajax.post(`${prefix}/sda/host_group_plan/update`, data))
  },

  async deleteHostGroupPlan(id, params) {
    return await formPromise(ajax.delete(`${prefix}/sda/host_group_plan/delete/${id}`, { params }))
  },
  
  // 标签规则配置相关API
  async getTagRuleList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/tag_rule/list`, {
      params
    }))
  },

  async addTagRule(data) {
    return await formPromise(ajax.post(`${prefix}/sda/tag_rule/create`, data))
  },

  async updateTagRule(data) {
    return await formPromise(ajax.patch(`${prefix}/sda/tag_rule/update`, data))
  },

  async deleteTagRule(id, params) {
    return await formPromise(ajax.delete(`${prefix}/sda/tag_rule/delete/${id}`, { params }))
  },

  // 主机标签配置相关API
  async getHostTagList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/host_tag/list`, {
      params
    }))
  },

    // 主机标签规划 - IP列表
  async getHostPlanIPList(params) {
    return await formPromise(ajax.get(`${prefix}/sda/host_tag/host/list`, {
      params
    }))
  },

  async createHostTag(data) {
    return await formPromise(ajax.post(`${prefix}/sda/host_tag/create`, data))
  },

  async updateHostTag(data) {
    return await formPromise(ajax.patch(`${prefix}/sda/host_tag/update`, data))
  },

  async deleteHostTag(data) {
    return await formPromise(ajax.delete(`${prefix}/sda/host_tag/delete`, { data }))
  },

  // 获取模块选项
  async getModuleOptions() {
    return await formPromise(ajax.get(`${prefix}/axe/host_modules`))
  },

  // 获取资源分组
  async getResourceGroupOptions() {
    return await formPromise(ajax.get(`${prefix}/rap/resource_group`))
  },

  // 业务标签
  async getBizLabelOptions() {
    return await formPromise(ajax.get(`${prefix}/rap/biz_tag`))
  },

  // lvs组
  async getLvsGroupOptions() {
    return await formPromise(ajax.get(`${prefix}/rap/lvs_group`))
  },
}
