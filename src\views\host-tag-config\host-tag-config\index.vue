<template>
  <el-card>
    <!-- 查询条件 -->
    <el-form :model="searchForm" ref="searchForm" label-position="left" inline>
      <el-row>
        <el-form-item label="IP地址" prop="ip">
          <el-input
            v-model="searchForm.ip"
            clearable
            placeholder="请输入IP地址"
            style="width: 200px"
          ></el-input>
        </el-form-item>

        <el-form-item label="CDN类型" prop="cdn_type">
          <el-select
            v-model="searchForm.cdn_type"
            placeholder="请选择CDN类型"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in cdnTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="模块" prop="module">
          <el-select
            v-model="searchForm.module"
            placeholder="请选择模块"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="item in moduleOptions"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="标签键" prop="tag_search">
          <el-select
            v-model="searchForm.tag_search"
            clearable
            placeholder="请输入标签键"
            style="width: 200px"
          >
          <el-option v-for="itm in tagOptions" :key="itm.id" :value="itm.tag_key" :label="itm.tag_key"></el-option>
        </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>

    <!-- 操作按钮 -->
    <el-row style="margin-bottom: 15px;">
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button
        type="warning"
        @click="handleBatchEdit"
        :disabled="selectedRows.length === 0"
      >
        批量修改
      </el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :disabled="selectedRows.length === 0"
      >
        批量删除
      </el-button>
    </el-row>

    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      v-loading="querying"
      border
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column prop="ip" label="IP地址" align="center" width="150">
      </el-table-column>

      <el-table-column prop="cdn_type" label="CDN类型" align="center" width="120">
      </el-table-column>

      <el-table-column prop="module" label="模块" align="center" width="150">
      </el-table-column>

      <el-table-column label="标签信息" align="center" min-width="300">
        <template slot-scope="scope">
          <div v-if="scope.row.tag_infos && scope.row.tag_infos.length > 0">
            <el-tag
              v-for="(tag, index) in scope.row.tag_infos"
              :key="index"
              style="margin: 2px;"
              size="small"
            >
              {{ tag.tag_key }}: {{ tag.tag_value }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column prop="operator" label="操作人" align="center" width="120">
      </el-table-column>

      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
          <el-button type="text" @click="handleDelete(scope.row)" style="color: #f56c6c;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-row class="pager">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.page_size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
      ></el-pagination>
    </el-row>

    <!-- 新增/修改弹窗 -->
    <host-tag-dialog
      :visible="dialogVisible"
      :is-edit="isEdit"
      :is-batch="isBatch"
      :current-row-data="currentRowData"
      :selected-rows="selectedRows"
      @close="dialogVisible = false"
      @refresh="onSearch"
    ></host-tag-dialog>

  </el-card>
</template>

<script>
import http from "@/views/host-tag-config/http.js";
import hostTagDialog from "./dialog/hostTagDialog.vue";

export default {
  name: "host-tag-config",
  components: {
    hostTagDialog,
  },
  data() {
    return {
      querying: false,
      tableData: [],
      selectedRows: [],
      dialogVisible: false,
      isEdit: false,
      isBatch: false,
      currentRowData: {},

      // 查询表单
      searchForm: {
        ip: "",
        cdn_type: "",
        module: "",
        tag_search: "",
      },

      // 分页信息
      pagination: {
        total: 0,
        page: 1,
        page_size: 20,
      },

      // 选项数据
      cdnTypeOptions: [
        { label: "PCDN", value: "2" },
        { label: "LCDN", value: "4" },
      ],
      moduleOptions: [],
      tagOptions: [],
    };
  },

  created() {
    this.loadOptions();
  },

  mounted() {
    this.onSearch();
  },

  methods: {
    // 加载选项数据
    async loadOptions() {
      try {
        // 加载模块选项
        const moduleRes = await http.getModuleOptions();
        if (moduleRes) {
          this.moduleOptions = moduleRes || [];
        }

        // 加载标签选项
        const tagRes = await http.getTagList({ page_size: 1000000 });
        if (tagRes) {
          this.tagOptions = tagRes.data?.items || [];
        }
      } catch (error) {
        console.error("加载选项数据失败:", error);
      }
    },

    // 查询数据
    async query() {
      const params = {
        ...this.searchForm,
        page: this.pagination.page,
        page_size: this.pagination.page_size,
      };

      // 处理标签搜索参数
      if (params.tag_search) {
        params.tag_key = params.tag_search;
        delete params.tag_search;
      }

      this.querying = true;
      try {
        const res = await http.getHostTagList(params);
        if (res && res.code === 100000) {
          this.tableData = res.data.items || [];
          this.pagination.total = res.data.total || 0;
        } else {
          this.tableData = [];
          this.pagination.total = 0;
        }
      } catch (error) {
        console.error("查询失败:", error);
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        this.querying = false;
      }
    },

    // 搜索
    onSearch() {
      this.pagination.page = 1;
      this.query();
    },

    // 重置
    onReset() {
      this.searchForm = {
        ip: "",
        cdn_type: "",
        module: "",
        tag_search: "",
      };
      this.onSearch();
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.page_size = val;
      this.query();
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.query();
    },

    // 新增
    handleAdd() {
      this.isEdit = false;
      this.isBatch = false;
      this.currentRowData = {};
      this.dialogVisible = true;
    },

    // 修改
    handleEdit(row) {
      this.isEdit = true;
      this.isBatch = false;
      this.currentRowData = { ...row };
      this.dialogVisible = true;
    },

    // 批量修改
    handleBatchEdit() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要修改的记录");
        return;
      }
      this.isEdit = true;
      this.isBatch = true;
      this.currentRowData = {};
      this.dialogVisible = true;
    },

    // 删除单条记录
    async handleDelete(row) {
      this.$confirm(`确定要删除IP地址为 ${row.ip} 的主机标签吗？`, "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          try {
            const params = {
              ips: [row.ip],
              operator: window.localStorage.getItem("userInfo") || "system",
            };

            const res = await http.deleteHostTag(params);
            if (res && res.code === 100000) {
              this.$message.success("删除成功");
              this.onSearch();
            }
          } catch (error) {
            console.error("删除失败:", error);
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的记录");
        return;
      }

      const ips = this.selectedRows.map(row => row.ip);
      this.$confirm(
        `确定要删除选中的 ${this.selectedRows.length} 条记录吗？`,
        "批量删除确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(async () => {
          try {
            const params = {
              ips: ips,
              operator: window.localStorage.getItem("userInfo") || "system",
            };

            const res = await http.deleteHostTag(params);
            if (res && res.code === 100000) {
              this.$message.success("批量删除成功");
              this.onSearch();
            }
          } catch (error) {
            console.error("批量删除失败:", error);
          }
        })
        .catch(() => {
          // 用户取消删除
        });
    },
  },
};
</script>

<style scoped>
.pager {
  margin-top: 20px;
  text-align: right;
}
</style>
